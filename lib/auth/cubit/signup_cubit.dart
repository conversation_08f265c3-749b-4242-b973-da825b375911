import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/auth/models/confirmed_password.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/name.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'signup_state.dart';

/// {@template signup_cubit}
/// A [Cubit] which manages the signup form state.
/// {@endtemplate}
class SignupCubit extends Cubit<SignupState> {
  /// {@macro signup_cubit}
  SignupCubit(this._authRepository) : super(const SignupState()) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'SignupCubit initialized',
      ),
    );
  }

  final AuthRepository _authRepository;
  final LoggerService _logger = LoggerService();

  /// Updates the name input.
  void nameChanged(String value) {
    final name = Name.dirty(value);
    emit(
      state.copyWith(
        name: name,
        status: _getFormStatus(
          name,
          state.email,
          state.password,
          state.confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the email input.
  void emailChanged(String value) {
    final email = Email.dirty(value);
    emit(
      state.copyWith(
        email: email,
        status: _getFormStatus(
          state.name,
          email,
          state.password,
          state.confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the password input.
  void passwordChanged(String value) {
    final password = Password.dirty(value);
    final confirmedPassword = ConfirmedPassword.dirty(
      password: password.value,
      value: state.confirmedPassword.value,
    );
    emit(
      state.copyWith(
        password: password,
        confirmedPassword: confirmedPassword,
        status: _getFormStatus(
          state.name,
          state.email,
          password,
          confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the confirmed password input.
  void confirmedPasswordChanged(String value) {
    final confirmedPassword = ConfirmedPassword.dirty(
      password: state.password.value,
      value: value,
    );
    emit(
      state.copyWith(
        confirmedPassword: confirmedPassword,
        status: _getFormStatus(
          state.name,
          state.email,
          state.password,
          confirmedPassword,
        ),
      ),
    );
  }

  /// Marks the name field as touched.
  void nameTouched() {
    emit(state.copyWith(nameTouched: true));
  }

  /// Marks the email field as touched.
  void emailTouched() {
    emit(state.copyWith(emailTouched: true));
  }

  /// Marks the password field as touched.
  void passwordTouched() {
    emit(state.copyWith(passwordTouched: true));
  }

  /// Marks the confirmed password field as touched.
  void confirmedPasswordTouched() {
    emit(state.copyWith(confirmedPasswordTouched: true));
  }

  /// Gets the form status based on validation
  FormStatus _getFormStatus(
    Name name,
    Email email,
    Password password,
    ConfirmedPassword confirmedPassword,
  ) {
    return name.isValid &&
            email.isValid &&
            password.isValid &&
            confirmedPassword.isValid
        ? FormStatus.valid
        : FormStatus.invalid;
  }

  /// Submits the signup form.
  Future<void> signUpFormSubmitted() async {
    if (!state.status.isValidated) {
      _logger.logAuth(
        LoggingConstants.signupAttempt,
        'blocked',
        state.email.value,
        'Form validation failed',
      );
      return;
    }

    final email = state.email.value;
    final name = state.name.value;
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.signupAttempt,
      'started',
      email,
      'Name: $name, Form validation passed',
    );

    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.signUp(
        email: email,
        password: state.password.value,
        name: name,
      );

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Signup registration',
          duration,
          'Email: $email, Name: $name',
        )
        ..logAuth(
          LoggingConstants.signupSuccess,
          'completed',
          email,
          'Registration successful for: $name',
        );

      emit(state.copyWith(status: FormStatus.submissionSuccess));
    } on SignUpWithEmailAndPasswordFailure catch (e) {
      _logger.logAuth(
        LoggingConstants.signupFailure,
        'failed',
        email,
        'Registration error: ${e.message}',
      );

      emit(
        state.copyWith(
          errorMessage: e.message,
          status: FormStatus.submissionFailure,
        ),
      );
    } catch (error, stackTrace) {
      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Signup process - Email: $email, Name: $name',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.signupFailure,
          'error',
          email,
          'Unexpected error during registration',
        );

      emit(state.copyWith(status: FormStatus.submissionFailure));
    }
  }
}
