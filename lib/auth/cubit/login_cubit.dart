import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'login_state.dart';

/// {@template login_cubit}
/// A [Cubit] which manages the login form state.
/// {@endtemplate}
class LoginCubit extends Cubit<LoginState> {
  /// {@macro login_cubit}
  LoginCubit(this._authRepository) : super(const LoginState()) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'LoginCubit initialized',
      ),
    );
  }

  final AuthRepository _authRepository;
  final LoggerService _logger = LoggerService();

  /// Updates the email input.
  void emailChanged(String value) {
    _logger.logFormValidation(
      'LoginForm',
      'email',
      'changed',
      'Length: ${value.length}',
    );

    final email = Email.dirty(value);
    emit(
      state.copyWith(
        email: email,
        status: _getFormStatus(email, state.password),
      ),
    );

    if (!email.isValid && value.isNotEmpty) {
      _logger.logFormValidation(
        'LoginForm',
        'email',
        'validation_failed',
        'Invalid email format',
      );
    }
  }

  /// Updates the password input.
  void passwordChanged(String value) {
    _logger.logFormValidation(
      'LoginForm',
      'password',
      'changed',
      'Length: ${value.length}',
    );

    final password = Password.dirty(value);
    emit(
      state.copyWith(
        password: password,
        status: _getFormStatus(state.email, password),
      ),
    );

    if (!password.isValid && value.isNotEmpty) {
      _logger.logFormValidation(
        'LoginForm',
        'password',
        'validation_failed',
        'Password does not meet requirements',
      );
    }
  }

  /// Marks the email field as touched.
  void emailTouched() {
    _logger.logFormValidation('LoginForm', 'email', 'touched');
    emit(state.copyWith(emailTouched: true));
  }

  /// Marks the password field as touched.
  void passwordTouched() {
    _logger.logFormValidation('LoginForm', 'password', 'touched');
    emit(state.copyWith(passwordTouched: true));
  }

  /// Gets the form status based on validation
  FormStatus _getFormStatus(Email email, Password password) {
    return email.isValid && password.isValid
        ? FormStatus.valid
        : FormStatus.invalid;
  }

  /// Submits the login form.
  Future<void> logInWithCredentials() async {
    if (!state.status.isValidated) {
      _logger.logAuth(
        LoggingConstants.loginAttempt,
        'blocked',
        state.email.value,
        'Form validation failed',
      );
      return;
    }

    final email = state.email.value;
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.loginAttempt,
      'started',
      email,
      'Form validation passed',
    );

    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.logInWithEmailAndPassword(
        email: email,
        password: state.password.value,
      );

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Login authentication',
          duration,
          'Email: $email',
        )
        ..logAuth(
          LoggingConstants.loginSuccess,
          'completed',
          email,
          'Authentication successful',
        );

      emit(state.copyWith(status: FormStatus.submissionSuccess));
    } on LogInWithEmailAndPasswordFailure catch (e) {
      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        email,
        'Authentication error: ${e.message}',
      );

      emit(
        state.copyWith(
          errorMessage: e.message,
          status: FormStatus.submissionFailure,
        ),
      );
    } catch (error, stackTrace) {
      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Login process - Email: $email',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.loginFailure,
          'error',
          email,
          'Unexpected error during authentication',
        );

      emit(state.copyWith(status: FormStatus.submissionFailure));
    }
  }
}
