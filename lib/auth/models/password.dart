import 'package:formz/formz.dart';

/// Validation errors for the [Password] [FormzInput].
enum PasswordValidationError {
  /// Password field is empty.
  empty,

  /// Password is too short.
  tooShort,
}

/// Extension to get user-friendly error messages
extension PasswordValidationErrorX on PasswordValidationError {
  String get message {
    switch (this) {
      case PasswordValidationError.empty:
        return 'Please enter your password';
      case PasswordValidationError.tooShort:
        return 'Password must be at least 6 characters long';
    }
  }
}

/// {@template password}
/// Form input for a password input.
/// {@endtemplate}
class Password extends FormzInput<String, PasswordValidationError> {
  /// {@macro password}
  const Password.pure() : super.pure('');

  /// {@macro password}
  const Password.dirty([super.value = '']) : super.dirty();

  @override
  PasswordValidationError? validator(String? value) {
    if (value == null || value.isEmpty) {
      return PasswordValidationError.empty;
    }
    return value.length >= 6 ? null : PasswordValidationError.tooShort;
  }
}
